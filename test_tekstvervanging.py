#!/usr/bin/env python3
"""
Test de nieuwe tekstvervanging functionaliteit
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.api.portfolio_processor import (
    verwerk_portfolio_tekst, 
    get_filter_config, 
    set_filter_config,
    FilterConfig,
    TextReplacementRule,
    pas_tekst_vervangingen_toe
)

def test_tekstvervanging():
    """Test de nieuwe flexibele tekstvervanging functionaliteit"""
    
    print("🧪 Test: Flexibele Tekstvervanging Functionaliteit")
    print("=" * 60)
    
    # Test tekst met verschillende patronen
    test_tekst = """3.1 Competentie analyse

Dit is een voorbeeld van portfolio tekst.

Theoretische onderbouwing 1: Dit moet worden aangepast naar "Theoretische onderbouwing:"

Hier komt meer tekst.

Theoretische onderbouwing 2: Ook dit moet worden aangepast

3.2 Volgende sectie

Theoretische onderbouwing 3: En dit ook

Samenvattend kan gesteld worden dat dit een test is.
"""

    # Configureer tekstvervanging regels
    vervangings_regels = [
        TextReplacementRule(
            zoek_patroon="Theoretische onderbouwing \\d+:",
            vervang_met="Theoretische onderbouwing:",
            is_regex=True,
            prefix="",
            postfix=""
        )
    ]
    
    # Stel nieuwe configuratie in
    nieuwe_config = FilterConfig(
        verwijder_termen=['samenvattend'],
        prefix="",
        postfix="",
        duplicaat_strategie="laatste",
        verwijder_sectie_tekst=True,
        tekst_vervangingen=vervangings_regels
    )
    
    set_filter_config(nieuwe_config)
    
    print("🔧 Configuratie ingesteld:")
    print(f"   Tekstvervanging regels: {len(vervangings_regels)}")
    print(f"   Patroon: '{vervangings_regels[0].zoek_patroon}'")
    print(f"   Vervang met: '{vervangings_regels[0].vervang_met}'")
    print()
    
    # Test alleen de tekstvervanging functie
    print("🔍 Test tekstvervanging functie:")
    aangepaste_tekst, vervangingen = pas_tekst_vervangingen_toe(test_tekst)
    
    print(f"   Aantal vervangingen: {len(vervangingen)}")
    for i, vervanging in enumerate(vervangingen, 1):
        print(f"   {i}. {vervanging}")
    print()
    
    # Test volledige verwerking
    print("📝 Test volledige portfolio verwerking:")
    try:
        resultaat = verwerk_portfolio_tekst(test_tekst)
        
        print("✅ Verwerking succesvol!")
        print(f"📊 Aantal secties: {resultaat['total_sections']}")
        print()
        
        # Toon wijzigingen
        wijzigingen = resultaat.get('wijzigingen', {})
        
        print("🔄 Toegepaste tekstvervanging:")
        tekst_vervangingen = wijzigingen.get('toegepaste_tekst_vervangingen', [])
        if tekst_vervangingen:
            for i, vervanging in enumerate(tekst_vervangingen, 1):
                print(f"   {i}. {vervanging}")
        else:
            print("   Geen vervangingen toegepast")
        print()
        
        print("🗑️ Verwijderde samenvattingen:")
        samenvattend = wijzigingen.get('verwijderde_samenvattend_paragrafen', [])
        if samenvattend:
            for i, paragraaf in enumerate(samenvattend, 1):
                print(f"   {i}. {paragraaf}")
        else:
            print("   Geen samenvattingen verwijderd")
        print()
        
        # Toon configuratie in resultaat
        filter_config = wijzigingen.get('filter_configuratie', {})
        tekst_vervangingen_config = filter_config.get('tekst_vervangingen', [])
        print("⚙️ Configuratie in resultaat:")
        print(f"   Aantal tekstvervanging regels: {len(tekst_vervangingen_config)}")
        if tekst_vervangingen_config:
            for i, regel in enumerate(tekst_vervangingen_config, 1):
                print(f"   {i}. Patroon: '{regel['zoek_patroon']}' → '{regel['vervang_met']}'")
                print(f"      Regex: {regel['is_regex']}, Prefix: '{regel['prefix']}', Postfix: '{regel['postfix']}'")
        
        print()
        print("📊 Statistieken:")
        stats = wijzigingen.get('statistieken', {})
        print(f"   Toegepaste vervangingen: {stats.get('aantal_toegepaste_vervangingen', 0)}")
        print(f"   Verwijderde samenvattingen: {stats.get('aantal_verwijderde_samenvattend', 0)}")
        print(f"   Gedetecteerde secties: {stats.get('totaal_gedetecteerde_secties', 0)}")
        
    except Exception as e:
        print(f"❌ Fout bij verwerking: {str(e)}")
        import traceback
        traceback.print_exc()

def test_verschillende_patronen():
    """Test verschillende soorten tekstvervanging patronen"""
    
    print("\n🧪 Test: Verschillende Vervangingspatronen")
    print("=" * 60)
    
    test_tekst = """3.1 Test sectie

Theoretische onderbouwing 1: Eerste onderbouwing
Theoretische onderbouwing 2: Tweede onderbouwing
Theoretische onderbouwing 10: Tiende onderbouwing

Samenvattend: dit moet weg

Conclusie: dit moet blijven
"""
    
    # Test verschillende regels
    regels = [
        # Regex patroon
        TextReplacementRule(
            zoek_patroon="Theoretische onderbouwing \\d+:",
            vervang_met="Theoretische onderbouwing:",
            is_regex=True
        ),
        # Eenvoudige string vervanging met prefix/postfix
        TextReplacementRule(
            zoek_patroon="Conclusie:",
            vervang_met="Eindconclusie",
            is_regex=False,
            prefix=">>> ",
            postfix=" <<<"
        )
    ]
    
    config = FilterConfig(
        verwijder_termen=['samenvattend'],
        tekst_vervangingen=regels
    )
    
    set_filter_config(config)
    
    aangepaste_tekst, vervangingen = pas_tekst_vervangingen_toe(test_tekst)
    
    print("🔄 Toegepaste vervangingen:")
    for i, vervanging in enumerate(vervangingen, 1):
        print(f"   {i}. {vervanging}")
    
    print("\n📝 Resultaat tekst (eerste 500 karakters):")
    print(aangepaste_tekst[:500])

if __name__ == "__main__":
    test_tekstvervanging()
    test_verschillende_patronen()
    print("\n✨ Alle tests voltooid!")
