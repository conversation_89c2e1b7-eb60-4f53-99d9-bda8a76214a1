/* Portfolio Correctie Tool - Styling */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
  margin-bottom: 20px;
}

/* Header */
header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 2px solid #e0e0e0;
}

header h1 {
  color: #2c3e50;
  font-size: 2.5em;
  margin-bottom: 10px;
  font-weight: 700;
}

.subtitle {
  color: #7f8c8d;
  font-size: 1.2em;
  font-weight: 300;
}

/* Main Layout */
main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.input-section,
.output-section,
.changes-section {
  background: #fff;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #e8e8e8;
}

.input-section h2,
.output-section h2,
.changes-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.5em;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Changes Section */
.changes-section {
  grid-column: 1 / -1;
  margin-top: 20px;
}

/* Tabs */
.changes-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.tab-btn {
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
  position: relative;
}

.tab-btn:hover {
  background: #f8f9fa;
  color: #333;
}

.tab-btn.active {
  background: #667eea;
  color: white;
}

.tab-btn.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: #667eea;
}

/* Tab Content */
.tab-content {
  min-height: 200px;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
}

/* Summary Stats */
.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.summary-icon {
  font-size: 24px;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.summary-label {
  font-size: 14px;
  color: #666;
  font-weight: 600;
}

.summary-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

/* Removed Content */
.removed-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.removed-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.2em;
  display: flex;
  align-items: center;
  gap: 10px;
}

.removed-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  background: #fafafa;
}

.removed-item {
  display: flex;
  gap: 10px;
  padding: 10px;
  margin-bottom: 10px;
  background: white;
  border-radius: 6px;
  border-left: 3px solid #dc3545;
}

.item-number {
  font-weight: 600;
  color: #dc3545;
  min-width: 30px;
}

.item-text {
  flex: 1;
  color: #555;
  line-height: 1.4;
}

/* Structure Content */
.structure-content h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.2em;
  display: flex;
  align-items: center;
  gap: 10px;
}

.structure-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.structure-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.structure-number {
  font-weight: 700;
  color: #2c3e50;
  font-size: 16px;
}

.structure-count {
  color: #666;
  font-size: 14px;
}

/* No Items Message */
.no-items {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

/* Configuration Panel */
.config-content h4 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.2em;
  display: flex;
  align-items: center;
  gap: 10px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.config-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.config-section h5 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.1em;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Terms Input */
.terms-input-container {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.terms-input-container input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.terms-input-container button {
  padding: 10px 15px;
  white-space: nowrap;
}

.terms-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
}

.term-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #667eea;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
}

.term-text {
  font-weight: 500;
}

.remove-term-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 12px;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.remove-term-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Radio and Checkbox Groups */
.radio-group,
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radio-group label,
.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #555;
}

.radio-group input,
.checkbox-group input {
  margin: 0;
}

/* Prefix/Postfix Inputs */
.prefix-postfix-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.input-group label {
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

.input-group input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

/* Config Actions */
.config-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-top: 10px;
  border-top: 1px solid #e0e0e0;
}

.config-actions button {
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
}

/* Config Messages */
.config-message {
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-weight: 500;
  animation: slideIn 0.3s ease;
}

.config-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.config-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Textarea Styling */
.textarea-container {
  position: relative;
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #555;
}

textarea {
  width: 100%;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-family: "Consolas", "Monaco", monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.3s ease;
  background: #fafafa;
}

textarea:focus {
  outline: none;
  border-color: #667eea;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

textarea::placeholder {
  color: #999;
  font-style: italic;
}

.char-count {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 12px;
  color: #888;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Buttons */
.controls,
.output-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.btn-primary,
.btn-secondary {
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #f8f9fa;
  color: #495057;
  border: 2px solid #e9ecef;
}

.btn-secondary:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #dee2e6;
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Format Toggle */
.format-toggle {
  display: flex;
  gap: 15px;
  margin-left: auto;
}

.format-toggle label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  cursor: pointer;
}

.format-toggle input[type="radio"] {
  margin: 0;
}

/* Statistics */
.stats {
  display: flex;
  gap: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  font-weight: 600;
  text-transform: uppercase;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
}

/* Error Message */
.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #dc3545;
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
  z-index: 1000;
  max-width: 400px;
  animation: slideIn 0.3s ease;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.error-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  margin-left: auto;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Footer */
footer {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  padding: 20px 0;
  border-top: 2px solid #e0e0e0;
  margin-top: 20px;
}

.info-section h3 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.info-section ul {
  list-style: none;
  padding-left: 0;
}

.info-section li {
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.info-section li::before {
  content: "•";
  color: #667eea;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.api-info {
  text-align: right;
  color: #666;
}

.status-unknown {
  color: #ffc107;
}
.status-online {
  color: #28a745;
}
.status-offline {
  color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
  main {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  footer {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .api-info {
    text-align: center;
  }

  .controls,
  .output-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .format-toggle {
    margin-left: 0;
    justify-content: center;
  }

  /* Changes section responsive */
  .changes-tabs {
    flex-wrap: wrap;
    gap: 5px;
  }

  .tab-btn {
    flex: 1;
    min-width: 120px;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .structure-list {
    grid-template-columns: 1fr;
  }

  .removed-content {
    gap: 20px;
  }

  /* Config panel responsive */
  .prefix-postfix-inputs {
    grid-template-columns: 1fr;
  }

  .config-actions {
    flex-direction: column;
  }

  .config-actions button {
    width: 100%;
  }

  .terms-input-container {
    flex-direction: column;
  }
}
