// Portfolio Correctie Tool - JavaScript Functionaliteit

// Configuratie
const API_BASE_URL = "http://localhost:8800";

// DOM Elementen
const inputText = document.getElementById("input-text");
const outputText = document.getElementById("output-text");
const processBtn = document.getElementById("process-btn");
const clearBtn = document.getElementById("clear-btn");
const exampleBtn = document.getElementById("example-btn");
const copyBtn = document.getElementById("copy-btn");
const downloadBtn = document.getElementById("download-btn");
const charCount = document.getElementById("char-count");
const errorMessage = document.getElementById("error-message");
const stats = document.getElementById("stats");
const sectionCount = document.getElementById("section-count");
const paragraphCount = document.getElementById("paragraph-count");

// Wijzigingen sectie elementen
const changesSection = document.getElementById("changes-section");
const tabButtons = document.querySelectorAll(".tab-btn");
const tabPanels = document.querySelectorAll(".tab-panel");
const detectedSections = document.getElementById("detected-sections");
const removedSummaries = document.getElementById("removed-summaries");
const removedDuplicates = document.getElementById("removed-duplicates");
const appliedReplacements = document.getElementById("applied-replacements");
const appliedReplacementsList = document.getElementById(
  "applied-replacements-list"
);
const removedSummariesList = document.getElementById("removed-summaries-list");
const removedDuplicatesList = document.getElementById(
  "removed-duplicates-list"
);
const removedSectionTextsList = document.getElementById(
  "removed-section-texts-list"
);
const structureList = document.getElementById("structure-list");

// Configuratie elementen
const newTermInput = document.getElementById("new-term-input");
const addTermBtn = document.getElementById("add-term-btn");
const termsList = document.getElementById("terms-list");
const duplicaatStrategieRadios = document.querySelectorAll(
  'input[name="duplicaat-strategie"]'
);
const verwijderSectieTekstCheckbox = document.getElementById(
  "verwijder-sectie-tekst"
);
const prefixInput = document.getElementById("prefix-input");
const postfixInput = document.getElementById("postfix-input");
const saveConfigBtn = document.getElementById("save-config-btn");
const resetConfigBtn = document.getElementById("reset-config-btn");
const loadConfigBtn = document.getElementById("load-config-btn");
const processStatus = document.getElementById("process-status");
const apiStatus = document.getElementById("api-status");
const formatRadios = document.querySelectorAll('input[name="format"]');

// Tekstvervanging configuratie elementen
const replacementRules = document.getElementById("replacement-rules");
const searchPatternInput = document.getElementById("search-pattern");
const replaceWithInput = document.getElementById("replace-with");
const rulePrefixInput = document.getElementById("rule-prefix");
const rulePostfixInput = document.getElementById("rule-postfix");
const isRegexCheckbox = document.getElementById("is-regex");
const addRuleBtn = document.getElementById("add-rule-btn");

// State
let lastProcessedData = null;
let currentReplacementRules = [];

// Voorbeeld tekst
const EXAMPLE_TEXT = `3.1 Je maakt veiligheid bespreekbaar

Mevrouw X toont in de beschreven casus aan dat zij veiligheid bespreekbaar maakt binnen het gezin en het informele netwerk, en dat zij samen met hen zoekt naar wat een goede en veilige situatie is voor de jeugdige. Dit blijkt uit de wijze waarop zij het gesprek aangaat met moeder over het gedrag van zoon C en het effect van het aanwezige speelgoed op zijn gedrag.

3.2 Je stemt continu samen de verwachtingen af

Mevrouw X toont in de beschreven casus met moeder A en haar zoon B dat zij actief en doorlopend de verwachtingen afstemt met het informele netwerk. Dit blijkt uit de wijze waarop zij het initiatief heeft genomen om in overleg met moeder A een structurele betrokkenheid van haar zusje te organiseren bij de evaluatiegesprekken.

Samenvattend kan gesteld worden dat dit een goed voorbeeld is.

3.3 Je bemoedigt de jeugdige in zijn zoektocht naar zelfstandigheid

Mevrouw X toont in de beschreven situatie aan dat zij actief bijdraagt aan het bevorderen van zelfstandigheid en maatschappelijke participatie van zowel de jeugdige als de ouder.`;

// Event Listeners
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
  setupEventListeners();
  checkApiStatus();
});

function initializeApp() {
  updateCharCount();
  updateButtonStates();
}

function setupEventListeners() {
  // Input tekst monitoring
  inputText.addEventListener("input", function () {
    updateCharCount();
    updateButtonStates();
  });

  // Buttons
  processBtn.addEventListener("click", processPortfolio);
  clearBtn.addEventListener("click", clearAll);
  exampleBtn.addEventListener("click", loadExample);
  copyBtn.addEventListener("click", copyToClipboard);
  downloadBtn.addEventListener("click", downloadResult);

  // Format toggle
  formatRadios.forEach((radio) => {
    radio.addEventListener("change", updateOutputFormat);
  });

  // Tab functionaliteit voor wijzigingen sectie
  tabButtons.forEach((button) => {
    button.addEventListener("click", function () {
      switchTab(this.dataset.tab);
    });
  });

  // Configuratie event listeners
  addTermBtn.addEventListener("click", addNewTerm);
  newTermInput.addEventListener("keypress", function (e) {
    if (e.key === "Enter") {
      addNewTerm();
    }
  });
  saveConfigBtn.addEventListener("click", saveConfiguration);
  resetConfigBtn.addEventListener("click", resetConfiguration);
  loadConfigBtn.addEventListener("click", loadConfiguration);

  // Tekstvervanging event listeners
  addRuleBtn.addEventListener("click", addReplacementRule);
  searchPatternInput.addEventListener("keypress", function (e) {
    if (e.key === "Enter") {
      addReplacementRule();
    }
  });
}

function updateCharCount() {
  const count = inputText.value.length;
  charCount.textContent = `${count.toLocaleString()} karakters`;
}

function updateButtonStates() {
  const hasInput = inputText.value.trim().length > 0;
  const hasOutput = outputText.value.trim().length > 0;

  processBtn.disabled = !hasInput;
  copyBtn.disabled = !hasOutput;
  downloadBtn.disabled = !hasOutput;
}

async function checkApiStatus() {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    if (response.ok) {
      apiStatus.textContent = "Online";
      apiStatus.className = "status-online";
    } else {
      throw new Error("API niet beschikbaar");
    }
  } catch (error) {
    apiStatus.textContent = "Offline";
    apiStatus.className = "status-offline";
    console.warn("API status check failed:", error);
  }
}

async function processPortfolio() {
  const text = inputText.value.trim();

  if (!text) {
    showError("Voer eerst tekst in om te verwerken.");
    return;
  }

  // UI feedback
  setProcessingState(true);
  processStatus.textContent = "Verwerken...";

  try {
    const response = await fetch(`${API_BASE_URL}/process`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ tekst: text }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.success) {
      lastProcessedData = result.data;
      updateOutput();
      updateStats(result.data);
      updateChanges(result.data);
      processStatus.textContent = "Succesvol verwerkt";
      showStats();
      showChanges();
    } else {
      throw new Error(result.error || "Onbekende fout bij verwerking");
    }
  } catch (error) {
    console.error("Processing error:", error);
    showError(`Fout bij verwerking: ${error.message}`);
    processStatus.textContent = "Fout opgetreden";
  } finally {
    setProcessingState(false);
    updateButtonStates();
  }
}

function setProcessingState(isProcessing) {
  const btnText = processBtn.querySelector(".btn-text");
  const spinner = processBtn.querySelector(".loading-spinner");

  processBtn.disabled = isProcessing;

  if (isProcessing) {
    btnText.style.display = "none";
    spinner.style.display = "inline";
  } else {
    btnText.style.display = "inline";
    spinner.style.display = "none";
  }
}

function updateOutput() {
  if (!lastProcessedData) return;

  const selectedFormat = document.querySelector(
    'input[name="format"]:checked'
  ).value;

  if (selectedFormat === "json") {
    outputText.value = JSON.stringify(lastProcessedData, null, 2);
  } else {
    outputText.value = formatDataForDisplay(lastProcessedData);
  }
}

function formatDataForDisplay(data) {
  let formatted = "";

  // Sorteer secties numeriek
  const sortedSections = Object.entries(data.sections).sort((a, b) => {
    const parseSection = (section) => {
      const parts = section.split(".");
      return parts.map((part) => parseInt(part, 10));
    };

    const aParts = parseSection(a[0]);
    const bParts = parseSection(b[0]);

    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      const aVal = aParts[i] || 0;
      const bVal = bParts[i] || 0;
      if (aVal !== bVal) return aVal - bVal;
    }
    return 0;
  });

  // Voeg alleen de inhoud toe, geen headers of statistieken
  sortedSections.forEach(([sectionNum, sectionData], index) => {
    Object.entries(sectionData.paragraphs).forEach(([paraKey, paraData]) => {
      formatted += `${paraData.content}\n\n`;
    });

    // Voeg extra lege regel toe tussen secties (behalve na de laatste)
    if (index < sortedSections.length - 1) {
      formatted += `\n`;
    }
  });

  return formatted.trim(); // Verwijder trailing whitespace
}

function updateStats(data) {
  sectionCount.textContent = data.total_sections;

  const totalParagraphs = Object.values(data.sections).reduce(
    (sum, section) => sum + section.total_paragraphs,
    0
  );
  paragraphCount.textContent = totalParagraphs;
}

function showStats() {
  stats.style.display = "flex";
}

function showChanges() {
  changesSection.style.display = "block";
}

function updateChanges(data) {
  if (!data.wijzigingen) {
    return;
  }

  const wijzigingen = data.wijzigingen;
  const statistieken = wijzigingen.statistieken;

  // Update samenvatting statistieken
  detectedSections.textContent = statistieken.totaal_gedetecteerde_secties;
  removedSummaries.textContent = statistieken.aantal_verwijderde_samenvattend;
  removedDuplicates.textContent = statistieken.aantal_verwijderde_duplicaten;
  appliedReplacements.textContent =
    statistieken.aantal_toegepaste_vervangingen || 0;

  // Update toegepaste vervangingen
  updateRemovedList(
    appliedReplacementsList,
    wijzigingen.toegepaste_tekst_vervangingen,
    "Geen tekstvervanging toegepast"
  );

  // Update verwijderde samenvattende paragrafen
  updateRemovedList(
    removedSummariesList,
    wijzigingen.verwijderde_samenvattend_paragrafen,
    "Geen samenvattende paragrafen verwijderd"
  );

  // Update verwijderde dubbele paragrafen
  updateRemovedList(
    removedDuplicatesList,
    wijzigingen.verwijderde_dubbele_paragrafen,
    "Geen dubbele paragrafen verwijderd"
  );

  // Update verwijderde sectie teksten
  updateRemovedList(
    removedSectionTextsList,
    wijzigingen.verwijderde_sectie_teksten,
    "Geen sectie teksten verwijderd"
  );

  // Update sectie structuur
  updateStructureList(wijzigingen.sectie_structuur);
}

function updateRemovedList(container, items, emptyMessage) {
  container.innerHTML = "";

  if (!items || items.length === 0) {
    const noItems = document.createElement("p");
    noItems.className = "no-items";
    noItems.textContent = emptyMessage;
    container.appendChild(noItems);
    return;
  }

  items.forEach((item, index) => {
    const itemElement = document.createElement("div");
    itemElement.className = "removed-item";
    itemElement.innerHTML = `
      <span class="item-number">${index + 1}.</span>
      <span class="item-text">${item}</span>
    `;
    container.appendChild(itemElement);
  });
}

function updateStructureList(sectieStructuur) {
  structureList.innerHTML = "";

  if (!sectieStructuur || sectieStructuur.length === 0) {
    const noItems = document.createElement("p");
    noItems.className = "no-items";
    noItems.textContent = "Geen secties gedetecteerd";
    structureList.appendChild(noItems);
    return;
  }

  sectieStructuur.forEach((sectie) => {
    const sectieElement = document.createElement("div");
    sectieElement.className = "structure-item";
    sectieElement.innerHTML = `
      <span class="structure-number">${sectie.nummer}</span>
      <span class="structure-count">${sectie.paragraaf_count} paragrafen</span>
    `;
    structureList.appendChild(sectieElement);
  });
}

function switchTab(tabName) {
  // Update tab buttons
  tabButtons.forEach((btn) => {
    btn.classList.remove("active");
    if (btn.dataset.tab === tabName) {
      btn.classList.add("active");
    }
  });

  // Update tab panels
  tabPanels.forEach((panel) => {
    panel.classList.remove("active");
    if (panel.id === `${tabName}-panel`) {
      panel.classList.add("active");
    }
  });
}

// Configuratie functionaliteit
let currentTerms = [];

function addNewTerm() {
  const term = newTermInput.value.trim();
  if (term && !currentTerms.includes(term.toLowerCase())) {
    currentTerms.push(term.toLowerCase());
    updateTermsList();
    newTermInput.value = "";
  }
}

function removeTerm(term) {
  currentTerms = currentTerms.filter((t) => t !== term);
  updateTermsList();
}

function updateTermsList() {
  termsList.innerHTML = "";

  if (currentTerms.length === 0) {
    const noItems = document.createElement("p");
    noItems.className = "no-items";
    noItems.textContent = "Geen termen toegevoegd";
    termsList.appendChild(noItems);
    return;
  }

  currentTerms.forEach((term) => {
    const termElement = document.createElement("div");
    termElement.className = "term-item";
    termElement.innerHTML = `
      <span class="term-text">${term}</span>
      <button class="remove-term-btn" onclick="removeTerm('${term}')">❌</button>
    `;
    termsList.appendChild(termElement);
  });
}

async function loadConfiguration() {
  try {
    const response = await fetch(`${API_BASE_URL}/config`);
    const result = await response.json();

    if (result.success) {
      const config = result.config;

      // Update termen lijst
      currentTerms = [...config.verwijder_termen];
      updateTermsList();

      // Update duplicaat strategie
      duplicaatStrategieRadios.forEach((radio) => {
        radio.checked = radio.value === config.duplicaat_strategie;
      });

      // Update sectie tekst checkbox
      verwijderSectieTekstCheckbox.checked = config.verwijder_sectie_tekst;

      // Update prefix/postfix
      prefixInput.value = config.prefix || "";
      postfixInput.value = config.postfix || "";

      // Update tekstvervanging regels
      currentReplacementRules = config.tekst_vervangingen || [];
      updateReplacementRulesList();

      showMessage("Configuratie geladen", "success");
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    showMessage(`Fout bij laden configuratie: ${error.message}`, "error");
  }
}

async function saveConfiguration() {
  try {
    const duplicaatStrategie = document.querySelector(
      'input[name="duplicaat-strategie"]:checked'
    ).value;

    const configData = {
      verwijder_termen: currentTerms,
      prefix: prefixInput.value.trim(),
      postfix: postfixInput.value.trim(),
      duplicaat_strategie: duplicaatStrategie,
      verwijder_sectie_tekst: verwijderSectieTekstCheckbox.checked,
      tekst_vervangingen: currentReplacementRules,
    };

    const response = await fetch(`${API_BASE_URL}/config`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(configData),
    });

    const result = await response.json();

    if (result.success) {
      showMessage("Configuratie opgeslagen", "success");
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    showMessage(`Fout bij opslaan configuratie: ${error.message}`, "error");
  }
}

async function resetConfiguration() {
  try {
    const response = await fetch(`${API_BASE_URL}/config/reset`, {
      method: "POST",
    });

    const result = await response.json();

    if (result.success) {
      // Laad de gereset configuratie
      await loadConfiguration();
      showMessage("Configuratie gereset naar standaard", "success");
    } else {
      throw new Error(result.error);
    }
  } catch (error) {
    showMessage(`Fout bij resetten configuratie: ${error.message}`, "error");
  }
}

function showMessage(message, type) {
  // Tijdelijke melding tonen
  const messageElement = document.createElement("div");
  messageElement.className = `config-message ${type}`;
  messageElement.textContent = message;

  // Voeg toe aan configuratie sectie
  const configContent = document.querySelector(".config-content");
  configContent.insertBefore(messageElement, configContent.firstChild);

  // Verwijder na 3 seconden
  setTimeout(() => {
    messageElement.remove();
  }, 3000);
}

function updateOutputFormat() {
  if (lastProcessedData) {
    updateOutput();
  }
}

function clearAll() {
  inputText.value = "";
  outputText.value = "";
  lastProcessedData = null;
  stats.style.display = "none";
  changesSection.style.display = "none";
  processStatus.textContent = "Klaar voor verwerking";
  updateCharCount();
  updateButtonStates();
}

function loadExample() {
  inputText.value = EXAMPLE_TEXT;
  updateCharCount();
  updateButtonStates();
  inputText.focus();
}

async function copyToClipboard() {
  try {
    await navigator.clipboard.writeText(outputText.value);

    // Visual feedback
    const originalText = copyBtn.textContent;
    copyBtn.textContent = "✅ Gekopieerd!";
    copyBtn.style.background = "#28a745";

    setTimeout(() => {
      copyBtn.textContent = originalText;
      copyBtn.style.background = "";
    }, 2000);
  } catch (error) {
    console.error("Copy failed:", error);
    showError("Kopiëren naar klembord mislukt.");
  }
}

function downloadResult() {
  if (!outputText.value) return;

  const selectedFormat = document.querySelector(
    'input[name="format"]:checked'
  ).value;
  const filename = `portfolio_resultaat.${
    selectedFormat === "json" ? "json" : "txt"
  }`;
  const contentType =
    selectedFormat === "json" ? "application/json" : "text/plain";

  const blob = new Blob([outputText.value], { type: contentType });
  const url = URL.createObjectURL(blob);

  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

function showError(message) {
  const errorText = errorMessage.querySelector(".error-text");
  errorText.textContent = message;
  errorMessage.style.display = "block";

  // Auto-hide na 5 seconden
  setTimeout(() => {
    hideError();
  }, 5000);
}

function hideError() {
  errorMessage.style.display = "none";
}

// Tekstvervanging functionaliteit
function addReplacementRule() {
  const searchPattern = searchPatternInput.value.trim();
  const replaceWith = replaceWithInput.value.trim();
  const prefix = rulePrefixInput.value.trim();
  const postfix = rulePostfixInput.value.trim();
  const isRegex = isRegexCheckbox.checked;

  if (!searchPattern) {
    showMessage("Voer een zoekpatroon in", "error");
    return;
  }

  const newRule = {
    zoek_patroon: searchPattern,
    vervang_met: replaceWith,
    prefix: prefix,
    postfix: postfix,
    is_regex: isRegex,
  };

  currentReplacementRules.push(newRule);
  updateReplacementRulesList();

  // Clear inputs
  searchPatternInput.value = "";
  replaceWithInput.value = "";
  rulePrefixInput.value = "";
  rulePostfixInput.value = "";
  isRegexCheckbox.checked = false;
}

function removeReplacementRule(index) {
  currentReplacementRules.splice(index, 1);
  updateReplacementRulesList();
}

function updateReplacementRulesList() {
  replacementRules.innerHTML = "";

  if (currentReplacementRules.length === 0) {
    const noItems = document.createElement("p");
    noItems.className = "no-items";
    noItems.textContent = "Geen vervangingsregels toegevoegd";
    replacementRules.appendChild(noItems);
    return;
  }

  currentReplacementRules.forEach((rule, index) => {
    const ruleElement = document.createElement("div");
    ruleElement.className = "replacement-rule-item";

    const patternDisplay = rule.is_regex
      ? `/${rule.zoek_patroon}/`
      : `"${rule.zoek_patroon}"`;
    const replacementDisplay = rule.prefix + rule.vervang_met + rule.postfix;

    ruleElement.innerHTML = `
      <div class="rule-content">
        <div class="rule-pattern">
          <span class="rule-label">Patroon:</span>
          <span class="rule-value">${patternDisplay}</span>
          ${rule.is_regex ? '<span class="regex-badge">REGEX</span>' : ""}
        </div>
        <div class="rule-replacement">
          <span class="rule-label">Vervang met:</span>
          <span class="rule-value">"${replacementDisplay}"</span>
        </div>
      </div>
      <button class="remove-rule-btn" onclick="removeReplacementRule(${index})">❌</button>
    `;
    replacementRules.appendChild(ruleElement);
  });
}

// Keyboard shortcuts
document.addEventListener("keydown", function (e) {
  // Ctrl+Enter: Process
  if (e.ctrlKey && e.key === "Enter") {
    e.preventDefault();
    if (!processBtn.disabled) {
      processPortfolio();
    }
  }

  // Ctrl+L: Load example
  if (e.ctrlKey && e.key === "l") {
    e.preventDefault();
    loadExample();
  }

  // Ctrl+K: Clear
  if (e.ctrlKey && e.key === "k") {
    e.preventDefault();
    clearAll();
  }
});

// Initialisatie
document.addEventListener("DOMContentLoaded", function () {
  setupEventListeners();
  updateCharCount();
  updateButtonStates();
  checkApiStatus();
  loadConfiguration(); // Laad configuratie bij opstarten
});
