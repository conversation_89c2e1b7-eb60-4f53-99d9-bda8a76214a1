#!/bin/bash

# Portfolio Correctie Systeem - Installatiescript voor Ubuntu 22.04 (Deel 1)
# -------------------------------------------------------------------------
# Dit script moet op de server worden uitgevoerd!

echo "🚀 Portfolio Correctie Systeem - Server Installatie (Deel 1)"
echo "=========================================================="
echo "⚠️  Dit script moet op de Ubuntu 22.04 server worden uitgevoerd!"
echo "⚠️  Niet op je lokale machine."
echo ""
echo "Druk op ENTER om door te gaan of CTRL+C om te annuleren..."
read

# Update systeem
echo "📦 Systeem updaten..."
sudo apt update && sudo apt upgrade -y

# Installeer benodigde pakketten
echo "📦 Benodigde pakketten installeren..."
sudo apt install -y python3 python3-pip python3-venv nginx git

# Maak applicatie directory in /opt
echo "📂 Applicatie directory aanmaken in /opt..."
sudo mkdir -p /opt/portfolio_correctie
sudo chown $USER:$USER /opt/portfolio_correctie
cd /opt/portfolio_correctie

# Maak Python virtual environment
echo "🐍 Python virtual environment aanmaken..."
python3 -m venv venv
source venv/bin/activate

# Maak directories voor backend en frontend
echo "📂 Project structuur aanmaken..."
mkdir -p backend frontend

echo "✅ Basisinstallatie voltooid!"
echo ""
echo "🔄 Kopieer nu je bestanden VANAF JE LOKALE MACHINE naar de server met:"
echo "   scp -r backend/* gebruiker@server:/opt/portfolio_correctie/backend/"
echo "   scp -r frontend/* gebruiker@server:/opt/portfolio_correctie/frontend/"
echo "   scp configure_portfolio_correctie.sh gebruiker@server:/opt/portfolio_correctie/"
echo ""
echo "📋 Na het kopiëren, voer het volgende uit op de server:"
echo "   cd /opt/portfolio_correctie"
echo "   chmod +x configure_portfolio_correctie.sh"
echo "   ./configure_portfolio_correctie.sh"
echo ""
