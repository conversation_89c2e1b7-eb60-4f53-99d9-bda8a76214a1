from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List

from .portfolio_processor import (
    verwerk_portfolio_tekst,
    get_filter_config,
    set_filter_config,
    update_filter_config,
    FilterConfig
)

# FastAPI app initialisatie
app = FastAPI(
    title="Portfolio Correctie API",
    description="API voor het verwerken van portfolio documenten naar gestructureerde JSON",
    version="1.0.0"
)

# CORS middleware toevoegen voor frontend communicatie
# USECASE: Staat frontend toe om API aan te roepen vanaf andere poorten/domeinen
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In productie: specificeer exacte frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response modellen
class PortfolioInput(BaseModel):
    """
    Input model voor portfolio tekst

    USECASE: Val<PERSON>ert dat de frontend correcte data stuurt
    """
    tekst: str

    class Config:
        schema_extra = {
            "example": {
                "tekst": "3.1 Je maakt veiligheid bespreekbaar\n\nDit is een voorbeeld van portfolio tekst..."
            }
        }

class PortfolioOutput(BaseModel):
    """
    Output model voor verwerkte portfolio data

    USECASE: Gestructureerde response voor frontend
    """
    success: bool
    data: Dict[str, Any] = None
    error: str = None

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "total_sections": 2,
                    "sections": {
                        "3.1": {
                            "title": "3.1",
                            "total_paragraphs": 3,
                            "paragraphs": {
                                "paragraph_1": {"content": "3.1"},
                                "paragraph_2": {"content": "Je maakt veiligheid bespreekbaar"},
                                "paragraph_3": {"content": "Dit is een voorbeeld..."}
                            }
                        }
                    }
                },
                "error": None
            }
        }

class FilterConfigInput(BaseModel):
    """Input model voor filter configuratie"""
    verwijder_termen: List[str]
    prefix: str = ""
    postfix: str = ""
    duplicaat_strategie: str = "laatste"
    verwijder_sectie_tekst: bool = True

    class Config:
        schema_extra = {
            "example": {
                "verwijder_termen": ["samenvattend", "theoretische onderbouwing"],
                "prefix": "",
                "postfix": ":",
                "duplicaat_strategie": "laatste",
                "verwijder_sectie_tekst": True
            }
        }

class FilterConfigOutput(BaseModel):
    """Output model voor filter configuratie"""
    success: bool
    config: Dict[str, Any] = None
    error: str = None

# API Endpoints
@app.get("/")
async def root():
    """
    Health check endpoint

    USECASE: Controleren of de API draait
    """
    return {
        "message": "Portfolio Correctie API is actief",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    """
    Gedetailleerde health check

    USECASE: Monitoring en debugging
    """
    return {
        "status": "healthy",
        "service": "portfolio-correctie-api",
        "version": "1.0.0"
    }

@app.post("/process", response_model=PortfolioOutput)
async def process_portfolio(input_data: PortfolioInput):
    """
    Hoofdendpoint voor portfolio verwerking

    USECASE: Ontvangt portfolio tekst van frontend en retourneert gestructureerde JSON

    Args:
        input_data: PortfolioInput object met de te verwerken tekst

    Returns:
        PortfolioOutput: Gestructureerde JSON met secties en paragrafen

    Raises:
        HTTPException: Bij verwerkingsfouten
    """
    try:
        # Valideer input
        if not input_data.tekst or not input_data.tekst.strip():
            raise HTTPException(
                status_code=400,
                detail="Tekst mag niet leeg zijn"
            )

        # Verwerk de portfolio tekst
        verwerkte_data = verwerk_portfolio_tekst(input_data.tekst)

        # Controleer of er secties gevonden zijn
        if not verwerkte_data.get("sections"):
            return PortfolioOutput(
                success=False,
                error="Geen geldige secties gevonden in de tekst. Zorg ervoor dat de tekst sectienummers bevat (bijv. 3.1, 4.2)."
            )

        return PortfolioOutput(
            success=True,
            data=verwerkte_data
        )

    except Exception as e:
        # Log de fout (in productie: gebruik proper logging)
        print(f"Fout bij verwerken portfolio: {str(e)}")

        return PortfolioOutput(
            success=False,
            error=f"Er is een fout opgetreden bij het verwerken: {str(e)}"
        )

@app.post("/process-json")
async def process_portfolio_raw_json(input_data: PortfolioInput):
    """
    Alternatief endpoint dat directe JSON retourneert

    USECASE: Voor directe integratie zonder wrapper object
    """
    try:
        if not input_data.tekst or not input_data.tekst.strip():
            raise HTTPException(
                status_code=400,
                detail="Tekst mag niet leeg zijn"
            )

        verwerkte_data = verwerk_portfolio_tekst(input_data.tekst)

        if not verwerkte_data.get("sections"):
            raise HTTPException(
                status_code=422,
                detail="Geen geldige secties gevonden in de tekst"
            )

        return verwerkte_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Verwerkingsfout: {str(e)}"
        )

# Configuratie endpoints
@app.get("/config", response_model=FilterConfigOutput)
async def get_filter_config_endpoint():
    """
    Haal de huidige filter configuratie op

    USECASE: Frontend kan huidige instellingen ophalen
    """
    try:
        config = get_filter_config()
        return FilterConfigOutput(
            success=True,
            config={
                "verwijder_termen": config.verwijder_termen,
                "prefix": config.prefix,
                "postfix": config.postfix,
                "duplicaat_strategie": config.duplicaat_strategie,
                "verwijder_sectie_tekst": config.verwijder_sectie_tekst
            }
        )
    except Exception as e:
        return FilterConfigOutput(
            success=False,
            error=f"Fout bij ophalen configuratie: {str(e)}"
        )

@app.post("/config", response_model=FilterConfigOutput)
async def update_filter_config_endpoint(config_input: FilterConfigInput):
    """
    Update de filter configuratie

    USECASE: Frontend kan instellingen wijzigen
    """
    try:
        # Valideer duplicaat strategie
        if config_input.duplicaat_strategie not in ["eerste", "laatste"]:
            raise HTTPException(
                status_code=400,
                detail="duplicaat_strategie moet 'eerste' of 'laatste' zijn"
            )

        # Update configuratie
        new_config = FilterConfig(
            verwijder_termen=config_input.verwijder_termen,
            prefix=config_input.prefix,
            postfix=config_input.postfix,
            duplicaat_strategie=config_input.duplicaat_strategie,
            verwijder_sectie_tekst=config_input.verwijder_sectie_tekst
        )

        set_filter_config(new_config)

        return FilterConfigOutput(
            success=True,
            config={
                "verwijder_termen": new_config.verwijder_termen,
                "prefix": new_config.prefix,
                "postfix": new_config.postfix,
                "duplicaat_strategie": new_config.duplicaat_strategie,
                "verwijder_sectie_tekst": new_config.verwijder_sectie_tekst
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        return FilterConfigOutput(
            success=False,
            error=f"Fout bij updaten configuratie: {str(e)}"
        )

@app.post("/config/reset", response_model=FilterConfigOutput)
async def reset_filter_config_endpoint():
    """
    Reset configuratie naar default waarden

    USECASE: Snel terugkeren naar standaard instellingen
    """
    try:
        from .portfolio_processor import DEFAULT_FILTER_CONFIG
        set_filter_config(DEFAULT_FILTER_CONFIG)

        return FilterConfigOutput(
            success=True,
            config={
                "verwijder_termen": DEFAULT_FILTER_CONFIG.verwijder_termen,
                "prefix": DEFAULT_FILTER_CONFIG.prefix,
                "postfix": DEFAULT_FILTER_CONFIG.postfix,
                "duplicaat_strategie": DEFAULT_FILTER_CONFIG.duplicaat_strategie,
                "verwijder_sectie_tekst": DEFAULT_FILTER_CONFIG.verwijder_sectie_tekst
            }
        )
    except Exception as e:
        return FilterConfigOutput(
            success=False,
            error=f"Fout bij resetten configuratie: {str(e)}"
        )

# Optioneel: Endpoint voor het ophalen van statistieken
@app.get("/stats")
async def get_processing_stats():
    """
    Endpoint voor basis statistieken

    USECASE: Monitoring en analytics
    """
    return {
        "supported_formats": ["txt"],
        "supported_section_patterns": ["X.Y (bijv. 3.1, 4.2)"],
        "features": [
            "Automatische sectie detectie",
            "Paragraaf splitsing",
            "Configureerbare tekst filtering",
            "Dubbele sectie verwijdering",
            "Numerieke sortering",
            "Sectie tekst verwijdering"
        ]
    }

if __name__ == "__main__":
    import uvicorn

    # USECASE: Lokale development server
    # In productie: gebruik gunicorn of vergelijkbare WSGI server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8800,
        reload=True,  # Auto-reload bij code wijzigingen
        log_level="info"
    )
