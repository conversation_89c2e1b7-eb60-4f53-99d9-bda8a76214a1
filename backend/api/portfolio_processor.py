import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# Configuratie voor tekst filtering
@dataclass
class TextReplacementRule:
    """Regel voor gedeeltelijke tekstvervanging"""
    # Patroon om te zoeken (kan regex zijn)
    zoek_patroon: str
    # Vervangende tekst (lege string = verwijderen)
    vervang_met: str = ""
    # Of dit een regex patroon is
    is_regex: bool = False
    # Prefix die toegevoegd wordt voor de vervanging
    prefix: str = ""
    # Postfix die toegevoegd wordt na de vervanging
    postfix: str = ""

@dataclass
class FilterConfig:
    """Configuratie voor flexibel tekst filtering"""
    # Termen die verwijderd moeten worden uit paragrafen (oude methode)
    verwijder_termen: List[str]
    # Prefix die toegevoegd wordt aan gefilterde tekst (oude methode)
    prefix: str = ""
    # Postfix die toegevoegd wordt aan gefilterde tekst (oude methode)
    postfix: str = ""
    # Strategie voor duplicaten: 'eerste' of 'laatste'
    duplicaat_strategie: str = "laatste"
    # Of tekst na sectienummers verwijderd moet worden
    verwijder_sectie_tekst: bool = True
    # Nieuwe flexibele tekstvervanging regels
    tekst_vervangingen: List[TextReplacementRule] = None

# Default configuratie
DEFAULT_FILTER_CONFIG = FilterConfig(
    verwijder_termen=['samenvattend', 'report a bug'],
    prefix="",
    postfix="",
    duplicaat_strategie="laatste",
    verwijder_sectie_tekst=True,
    tekst_vervangingen=[
        # Voorbeeld: "Theoretische onderbouwing 1:" wordt "Theoretische onderbouwing:"
        TextReplacementRule(
            zoek_patroon="Theoretische onderbouwing \\d+:",
            vervang_met="Theoretische onderbouwing:",
            is_regex=True,
            prefix="",
            postfix=""
        ),
        # Voeg dubbele punt toe aan titels die eindigen zonder dubbele punt
        # Bijvoorbeeld: "Eigen regie en vertegenwoordiging bij verstandelijke beperking" → "Eigen regie en vertegenwoordiging bij verstandelijke beperking:"
        TextReplacementRule(
            zoek_patroon="^([A-Z][^:\n]*[a-z])$",
            vervang_met="\\1:",
            is_regex=True,
            prefix="",
            postfix=""
        )
    ]
)

# Globale configuratie variabele
_current_config = DEFAULT_FILTER_CONFIG

def get_filter_config() -> FilterConfig:
    """Haal de huidige filter configuratie op"""
    return _current_config

def set_filter_config(config: FilterConfig) -> None:
    """Stel een nieuwe filter configuratie in"""
    global _current_config
    _current_config = config

def update_filter_config(**kwargs) -> FilterConfig:
    """Update specifieke configuratie waarden"""
    global _current_config
    config_dict = {
        'verwijder_termen': _current_config.verwijder_termen,
        'prefix': _current_config.prefix,
        'postfix': _current_config.postfix,
        'duplicaat_strategie': _current_config.duplicaat_strategie,
        'verwijder_sectie_tekst': _current_config.verwijder_sectie_tekst,
        'tekst_vervangingen': _current_config.tekst_vervangingen or []
    }
    config_dict.update(kwargs)
    _current_config = FilterConfig(**config_dict)
    return _current_config

def pas_tekst_vervangingen_toe(tekst: str, config: Optional[FilterConfig] = None) -> tuple[str, List[str]]:
    """
    Pas flexibele tekstvervanging regels toe op de gegeven tekst

    USECASE: Gedeeltelijke tekstverwijdering/vervanging in plaats van hele paragrafen verwijderen.
    Bijvoorbeeld: "Theoretische onderbouwing 1:" wordt "Theoretische onderbouwing:"

    Returns:
        tuple: (aangepaste_tekst, lijst_van_toegepaste_vervangingen)
    """
    if config is None:
        config = get_filter_config()

    if not config.tekst_vervangingen:
        return tekst, []

    aangepaste_tekst = tekst
    toegepaste_vervangingen = []

    for regel in config.tekst_vervangingen:
        try:
            if regel.is_regex:
                # Gebruik regex voor patroon matching met MULTILINE flag voor ^ en $ per regel
                matches = re.findall(regel.zoek_patroon, aangepaste_tekst, re.IGNORECASE | re.MULTILINE)
                if matches:
                    # Voor elke match, bereken de werkelijke vervanging
                    for match in matches:
                        # Bereken wat de werkelijke vervanging wordt
                        if isinstance(match, tuple):
                            # Als de match een tuple is (van groepen), gebruik de eerste groep
                            original_text = match[0] if match else str(match)
                        else:
                            original_text = match

                        # Bereken de werkelijke vervanging door de regex toe te passen op de match
                        werkelijke_vervanging = re.sub(regel.zoek_patroon, regel.prefix + regel.vervang_met + regel.postfix, original_text, flags=re.IGNORECASE | re.MULTILINE)
                        toegepaste_vervangingen.append(f"'{original_text}' → '{werkelijke_vervanging}'")

                    # Pas de vervanging toe op de hele tekst
                    vervanging = regel.prefix + regel.vervang_met + regel.postfix
                    aangepaste_tekst = re.sub(regel.zoek_patroon, vervanging, aangepaste_tekst, flags=re.IGNORECASE | re.MULTILINE)
            else:
                # Eenvoudige string vervanging
                if regel.zoek_patroon.lower() in aangepaste_tekst.lower():
                    vervanging = regel.prefix + regel.vervang_met + regel.postfix
                    # Case-insensitive vervanging
                    aangepaste_tekst = re.sub(re.escape(regel.zoek_patroon), vervanging, aangepaste_tekst, flags=re.IGNORECASE)
                    toegepaste_vervangingen.append(f"'{regel.zoek_patroon}' → '{vervanging}'")
        except re.error as e:
            # Als regex fout is, sla deze regel over
            toegepaste_vervangingen.append(f"FOUT in regex '{regel.zoek_patroon}': {str(e)}")
            continue

    return aangepaste_tekst, toegepaste_vervangingen

def verwijder_dubbele_secties(secties: List[Dict]) -> List[Dict]:
    """
    Verwijder dubbele sectienummers, behoud degene met de meeste inhoud

    USECASE: Soms bevatten documenten hetzelfde sectienummer meerdere keren
    (bijv. 3.1 komt twee keer voor). Dit kan gebeuren door:
    - Kopieer/plak fouten in het originele document
    - Herstructurering waarbij secties verplaatst maar niet verwijderd werden
    - Samenvoegen van meerdere documenten

    Deze functie zorgt ervoor dat we alleen de meest complete versie behouden,
    zodat de JSON output geen dubbele secties bevat.
    """
    sectie_dict = {}

    for sectie in secties:
        sectie_nummer = sectie['nummer']

        # Als we dit sectienummer nog niet hebben gezien, of als deze sectie meer inhoud heeft
        if (sectie_nummer not in sectie_dict or
            len(sectie['inhoud']) > len(sectie_dict[sectie_nummer]['inhoud'])):
            sectie_dict[sectie_nummer] = sectie

    # Geef secties terug in oorspronkelijke volgorde
    unieke_secties = []
    gezien_nummers = set()

    for sectie in secties:
        if sectie['nummer'] not in gezien_nummers:
            gezien_nummers.add(sectie['nummer'])
            unieke_secties.append(sectie_dict[sectie['nummer']])

    return unieke_secties

def verwijder_samenvattend_paragrafen(inhoud: str, config: Optional[FilterConfig] = None) -> tuple[str, list[str]]:
    """
    Verwijder paragrafen die beginnen met configureerbare termen

    USECASE: Portfolio documenten bevatten vaak automatisch gegenereerde teksten die:
    - Automatisch gegenereerd zijn door systemen
    - Redundante informatie bevatten die al in de hoofdtekst staat
    - De leesbaarheid van de uiteindelijke output verstoren
    - Niet relevant zijn voor de analyse van competenties

    Deze functie filtert deze paragrafen eruit terwijl belangrijke inhoud behouden blijft.
    Configureerbaar via FilterConfig voor flexibiliteit.

    Returns:
        tuple: (gefilterde_inhoud, lijst_van_verwijderde_paragrafen)
    """
    if config is None:
        config = get_filter_config()

    # Splits inhoud in paragrafen (door dubbele nieuwe regels)
    paragrafen = inhoud.split('\n\n')
    gefilterde_paragrafen = []
    verwijderde_paragrafen = []

    for paragraaf in paragrafen:
        # Sla alleen paragrafen over die beginnen met configureerbare termen
        regels_in_paragraaf = [regel.strip() for regel in paragraaf.split('\n') if regel.strip()]

        # Als de paragraaf slechts één regel heeft en begint met een te verwijderen term
        if (len(regels_in_paragraaf) == 1 and
            any(regels_in_paragraaf[0].lower().startswith(term.lower()) for term in config.verwijder_termen)):
            verwijderde_paragrafen.append(paragraaf.strip())
            continue

        # Als de paragraaf begint met een te verwijderen term maar meerdere regels heeft,
        # verwijder alleen de betreffende regel(s) en behoud de rest
        if (regels_in_paragraaf and
            any(regels_in_paragraaf[0].lower().startswith(term.lower()) for term in config.verwijder_termen)):

            gefilterde_regels = []
            verwijderde_regels = []

            for regel in paragraaf.split('\n'):
                regel_lower = regel.strip().lower()
                if any(regel_lower.startswith(term.lower()) for term in config.verwijder_termen):
                    verwijderde_regels.append(regel.strip())
                else:
                    gefilterde_regels.append(regel)

            # Voeg verwijderde regels toe aan tracking
            if verwijderde_regels:
                verwijderde_paragrafen.extend(verwijderde_regels)

            # Voeg alleen toe als er resterende regels zijn
            if any(regel.strip() for regel in gefilterde_regels):
                # Voeg prefix en postfix toe indien geconfigureerd
                gefilterde_tekst = '\n'.join(gefilterde_regels)
                if config.prefix or config.postfix:
                    gefilterde_tekst = f"{config.prefix}{gefilterde_tekst}{config.postfix}"
                gefilterde_paragrafen.append(gefilterde_tekst)
        else:
            # Behoud de paragraaf zoals deze is
            gefilterde_paragrafen.append(paragraaf)

    return '\n\n'.join(gefilterde_paragrafen), verwijderde_paragrafen

def verwijder_dubbele_paragrafen(paragrafen: List[str], config: Optional[FilterConfig] = None) -> tuple[List[str], List[str]]:
    """
    Verwijder dubbele paragrafen volgens configureerbare strategie

    USECASE: Soms bevatten documenten identieke paragrafen door:
    - Kopieer/plak fouten in het originele document
    - Herstructurering waarbij content gedupliceerd werd
    - Automatische generatie die duplicaten creëert

    Deze functie zorgt ervoor dat we duplicaten verwijderen volgens de geconfigureerde strategie,
    zodat de JSON output geen dubbele paragrafen bevat.

    Returns:
        tuple: (gefilterde_paragrafen, lijst_van_verwijderde_duplicaten)
    """
    if not paragrafen:
        return paragrafen, []

    if config is None:
        config = get_filter_config()

    # Houd bij welke content we al hebben gezien en op welke positie
    gezien_content = {}
    te_verwijderen_indices = set()
    verwijderde_duplicaten = []

    for i, paragraaf in enumerate(paragrafen):
        content = paragraaf.strip()

        # Sla lege paragrafen over
        if not content:
            continue

        # Als we deze content al eerder hebben gezien
        if content in gezien_content:
            eerdere_index = gezien_content[content]

            if config.duplicaat_strategie == "eerste":
                # Verwijder de huidige (latere) versie
                te_verwijderen_indices.add(i)
                verwijderde_duplicaten.append(paragraaf.strip())
            else:  # "laatste" (default)
                # Verwijder de eerdere versie
                te_verwijderen_indices.add(eerdere_index)
                verwijderde_duplicaten.append(paragrafen[eerdere_index].strip())
                # Update de positie naar de huidige versie
                gezien_content[content] = i
        else:
            # Eerste keer dat we deze content zien
            gezien_content[content] = i

    # Maak nieuwe lijst zonder de te verwijderen indices
    gefilterde_paragrafen = []
    for i, paragraaf in enumerate(paragrafen):
        if i not in te_verwijderen_indices:
            gefilterde_paragrafen.append(paragraaf)

    return gefilterde_paragrafen, verwijderde_duplicaten

def splits_inhoud_in_paragrafen(inhoud: str) -> tuple[Dict[str, Dict[str, str]], List[str], List[str]]:
    """
    Splits inhoud in paragrafen/regels als subnodes

    USECASE: Voor gedetailleerde analyse moeten we tekst opdelen in kleine, beheerbare stukken.
    Dit is nodig omdat:
    - AI-systemen beter werken met kleinere tekstblokken
    - Gebruikers specifieke paragrafen kunnen selecteren voor feedback
    - Elke paragraaf apart geanalyseerd kan worden op competenties
    - JSON-structuur overzichtelijker wordt met genummerde paragrafen

    Speciale behandeling: Sectienummers (3.1) en titels worden gescheiden in aparte paragrafen
    voor betere leesbaarheid en analyse. Herkent ook sectietitels die op een aparte regel staan.

    Returns:
        tuple: (subnodes_dict, lijst_van_verwijderde_duplicaten_in_sectie, lijst_van_verwijderde_sectie_teksten)
    """
    # Opmerking: "Samenvattend" paragrafen worden verwijderd op documentniveau, niet per sectie

    # Splits op dubbele nieuwe regels (paragrafen)
    paragrafen = inhoud.split('\n\n')

    # Verwerk paragrafen om sectienummers te behandelen
    verwerkte_paragrafen = []
    verwijderde_sectie_teksten = []
    i = 0

    while i < len(paragrafen):
        paragraaf = paragrafen[i].strip()
        if not paragraaf:
            i += 1
            continue

        # Controleer of paragraaf alleen een sectienummer is (bijv. "3.1")
        alleen_sectie_nummer_match = re.match(r'^(\d+\.\d+)$', paragraaf)

        if alleen_sectie_nummer_match:
            sectie_nummer = alleen_sectie_nummer_match.group(1)
            verwerkte_paragrafen.append(sectie_nummer)

            # Controleer of de volgende paragraaf een titel is
            if i + 1 < len(paragrafen):
                volgende_paragraaf = paragrafen[i + 1].strip()

                # Als de volgende paragraaf begint met een hoofdletter en geen sectienummer is,
                # behandel het als een titel
                if (volgende_paragraaf and
                    not re.match(r'^\d+\.\d+', volgende_paragraaf) and
                    volgende_paragraaf[0].isupper()):

                    verwerkte_paragrafen.append(volgende_paragraaf)
                    i += 2  # Sla beide paragrafen over
                    continue

            i += 1
            continue

        # Controleer of paragraaf begint met sectienummer gevolgd door tekst (bijv. "3.1 Titel tekst")
        sectie_met_titel_match = re.match(r'^(\d+\.\d+)\.?\s+(.+)$', paragraaf)
        if sectie_met_titel_match:
            sectie_nummer = sectie_met_titel_match.group(1)
            titel_tekst = sectie_met_titel_match.group(2)

            # Controleer configuratie of tekst na sectienummer verwijderd moet worden
            config = get_filter_config()
            if config.verwijder_sectie_tekst:
                # Voeg alleen het sectienummer toe, verwijder de tekst erachter
                verwerkte_paragrafen.append(sectie_nummer)
                # Track de verwijderde tekst
                verwijderde_sectie_teksten.append(titel_tekst)
            else:
                # Splits in twee aparte paragrafen (oude gedrag)
                verwerkte_paragrafen.append(sectie_nummer)
                verwerkte_paragrafen.append(titel_tekst)
        else:
            verwerkte_paragrafen.append(paragraaf)

        i += 1

    # Verwijder dubbele paragrafen (behoud alleen de laatste van identieke paragrafen)
    verwerkte_paragrafen, verwijderde_duplicaten = verwijder_dubbele_paragrafen(verwerkte_paragrafen)

    subnodes = {}
    for i, paragraaf in enumerate(verwerkte_paragrafen):
        subnodes[f"paragraph_{i+1}"] = {
            "content": paragraaf
        }

    return subnodes, verwijderde_duplicaten, verwijderde_sectie_teksten

def splits_tekst_in_secties(tekst_inhoud: str) -> List[Dict]:
    """
    Splits tekst in logische secties gebaseerd op genummerde patronen zoals 3.1, 3.2, etc.

    USECASE: Portfolio documenten zijn gestructureerd met genummerde competentie-secties.
    Deze functie is essentieel omdat:
    - Elke sectie een specifieke competentie of gedragsindicator vertegenwoordigt
    - Beoordelaars per sectie feedback moeten kunnen geven
    - JSON-output georganiseerd moet zijn per competentiegebied
    - Automatische analyse per competentie mogelijk wordt

    Speciale functionaliteit: Voegt automatisch lege regels toe na sectienummers
    voor consistente opmaak (bijv. na "3.1" komt altijd een lege regel).
    """
    # Patroon om sectiekoppen te matchen zoals "3.1", "4.2", etc.
    sectie_patroon = r'^(\d+\.\d+)(?:\s|$)'

    secties = []
    huidige_sectie = None
    huidige_inhoud = []
    intro_inhoud = []

    regels = tekst_inhoud.split('\n')

    for i, regel in enumerate(regels):
        # Controleer of regel begint met een sectienummer
        match = re.match(sectie_patroon, regel.strip())

        if match:
            # Bewaar vorige sectie als deze bestaat
            if huidige_sectie is not None:
                secties.append({
                    'nummer': huidige_sectie,
                    'inhoud': '\n'.join(huidige_inhoud)
                })

            # Start nieuwe sectie
            huidige_sectie = match.group(1)
            huidige_inhoud = [regel]

            # Controleer of de volgende regel bestaat en niet leeg is
            # Zo ja, voeg een lege regel toe na het sectienummer
            if (i + 1 < len(regels) and
                regels[i + 1].strip() != '' and
                not re.match(sectie_patroon, regels[i + 1].strip())):
                huidige_inhoud.append('')
        else:
            # Voeg regel toe aan huidige sectie of intro
            if huidige_sectie is not None:
                huidige_inhoud.append(regel)
            else:
                # Regels voor eerste sectie - intro inhoud
                intro_inhoud.append(regel)

    # Voeg intro sectie toe als deze inhoud heeft
    if intro_inhoud:
        secties.insert(0, {
            'nummer': 'intro',
            'inhoud': '\n'.join(intro_inhoud)
        })

    # Vergeet de laatste sectie niet
    if huidige_sectie is not None:
        secties.append({
            'nummer': huidige_sectie,
            'inhoud': '\n'.join(huidige_inhoud)
        })

    # Verwijder dubbele secties
    secties = verwijder_dubbele_secties(secties)

    return secties

def sorteer_sectie_nummer(sectie: Dict) -> List:
    """
    USECASE: Zorgt ervoor dat secties in logische volgorde staan (4.1, 4.2, 4.10)
    in plaats van alfabetische volgorde (4.1, 4.10, 4.2)
    """
    try:
        # Splits sectienummer zoals "4.1" in [4, 1] en converteer naar integers
        delen = sectie['nummer'].split('.')
        return [int(deel) for deel in delen]
    except ValueError:
        # Als conversie mislukt, gebruik oorspronkelijke string voor sortering
        return [float('inf'), sectie['nummer']]

def verwerk_portfolio_tekst(tekst_inhoud: str) -> Dict[str, Any]:
    """
    Hoofdfunctie die portfolio tekst omzet naar gestructureerde JSON

    USECASE: Converteert ongestructureerde portfolio tekst naar geanalyseerde JSON.
    Dit is de kern van het systeem omdat:
    - Portfolio documenten vaak ongestructureerde tekst zijn
    - JSON-formaat nodig is voor web-applicaties en databases
    - Hiërarchische structuur (secties → paragrafen) analyse vergemakkelijkt

    Workflow: TXT → Tekstvervanging → Samenvattend verwijderen → Secties splitsen → Paragrafen splitsen → JSON structuur
    """
    # Pas eerst flexibele tekstvervanging toe
    inhoud, toegepaste_vervangingen = pas_tekst_vervangingen_toe(tekst_inhoud)

    # Verwijder "Samenvattend" paragrafen van het hele document
    inhoud, verwijderde_samenvattend = verwijder_samenvattend_paragrafen(inhoud)

    # Splits in secties
    secties = splits_tekst_in_secties(inhoud)

    # Verzamel wijzigingsinformatie
    alle_verwijderde_duplicaten = []
    alle_verwijderde_sectie_teksten = []
    sectie_structuur = []

    # Maak JSON structuur
    json_data = {
        "total_sections": len(secties),
        "sections": {}
    }

    # Sorteer secties en voeg elke sectie toe aan de JSON structuur met subnodes
    for sectie in sorted(secties, key=sorteer_sectie_nummer):
        # Sla de intro sectie over
        if sectie['nummer'] == 'intro':
            continue

        # Splits inhoud in paragrafen als subnodes
        subnodes, sectie_duplicaten, sectie_teksten = splits_inhoud_in_paragrafen(sectie['inhoud'])
        alle_verwijderde_duplicaten.extend(sectie_duplicaten)
        alle_verwijderde_sectie_teksten.extend(sectie_teksten)

        # Voeg sectie toe aan structuur overzicht
        sectie_structuur.append({
            "nummer": sectie['nummer'],
            "paragraaf_count": len(subnodes)
        })

        json_data["sections"][sectie['nummer']] = {
            "title": sectie['nummer'],
            "total_paragraphs": len(subnodes),
            "paragraphs": subnodes
        }

    # Update totaal aantal secties na verwijdering intro
    json_data["total_sections"] = len(json_data["sections"])

    # Voeg wijzigingsinformatie toe
    json_data["wijzigingen"] = {
        "toegepaste_tekst_vervangingen": toegepaste_vervangingen,
        "verwijderde_samenvattend_paragrafen": verwijderde_samenvattend,
        "verwijderde_dubbele_paragrafen": alle_verwijderde_duplicaten,
        "verwijderde_sectie_teksten": alle_verwijderde_sectie_teksten,
        "sectie_structuur": sectie_structuur,
        "filter_configuratie": {
            "verwijder_termen": get_filter_config().verwijder_termen,
            "prefix": get_filter_config().prefix,
            "postfix": get_filter_config().postfix,
            "duplicaat_strategie": get_filter_config().duplicaat_strategie,
            "verwijder_sectie_tekst": get_filter_config().verwijder_sectie_tekst,
            "tekst_vervangingen": [
                {
                    "zoek_patroon": regel.zoek_patroon,
                    "vervang_met": regel.vervang_met,
                    "is_regex": regel.is_regex,
                    "prefix": regel.prefix,
                    "postfix": regel.postfix
                } for regel in (get_filter_config().tekst_vervangingen or [])
            ]
        },
        "statistieken": {
            "aantal_toegepaste_vervangingen": len(toegepaste_vervangingen),
            "aantal_verwijderde_samenvattend": len(verwijderde_samenvattend),
            "aantal_verwijderde_duplicaten": len(alle_verwijderde_duplicaten),
            "aantal_verwijderde_sectie_teksten": len(alle_verwijderde_sectie_teksten),
            "totaal_gedetecteerde_secties": len(sectie_structuur)
        }
    }

    return json_data
