#!/usr/bin/env python3
"""
Test script voor de backend functionaliteit
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

from api.portfolio_processor import verwerk_portfolio_tekst, get_filter_config, update_filter_config

def test_wijzigingen_functionaliteit():
    """Test de nieuwe wijzigingen functionaliteit"""

    test_tekst = """
3.1 Dit is tekst die verwijderd moet worden

Dit is een voorbeeld van portfolio tekst die geanalyseerd wordt op competenties.

Samenvattend kan gesteld worden dat dit een test is.

Theoretische onderbouwing 1: Dit moet ook verwijderd worden

3.2 Ook deze tekst moet weg

Hier komt de tekst voor de volgende competentie.

Dit is een voorbeeld van portfolio tekst die geanalyseerd wordt op competenties.

Samenvattend is dit een duplicaat test.
"""

    print("🧪 Test: Verwerking van portfolio tekst met wijzigingen tracking")
    print("=" * 60)

    try:
        resultaat = verwerk_portfolio_tekst(test_tekst)

        print("✅ Verwerking succesvol!")
        print(f"📊 Aantal secties: {resultaat['total_sections']}")

        if 'wijzigingen' in resultaat:
            wijzigingen = resultaat['wijzigingen']
            statistieken = wijzigingen['statistieken']

            print("\n🔍 Wijzigingen overzicht:")
            print(f"   📝 Gedetecteerde secties: {statistieken['totaal_gedetecteerde_secties']}")
            print(f"   🗑️ Verwijderde samenvattingen: {statistieken['aantal_verwijderde_samenvattend']}")
            print(f"   🔄 Verwijderde duplicaten: {statistieken['aantal_verwijderde_duplicaten']}")

            print("\n📋 Verwijderde samenvattende paragrafen:")
            for i, paragraaf in enumerate(wijzigingen['verwijderde_samenvattend_paragrafen'], 1):
                print(f"   {i}. {paragraaf}")

            print("\n📋 Verwijderde dubbele paragrafen:")
            for i, paragraaf in enumerate(wijzigingen['verwijderde_dubbele_paragrafen'], 1):
                print(f"   {i}. {paragraaf}")

            print("\n🏗️ Sectie structuur:")
            for sectie in wijzigingen['sectie_structuur']:
                print(f"   {sectie['nummer']}: {sectie['paragraaf_count']} paragrafen")

            print("\n📋 Verwijderde sectie teksten:")
            for i, tekst in enumerate(wijzigingen.get('verwijderde_sectie_teksten', []), 1):
                print(f"   {i}. {tekst}")

            print("\n⚙️ Filter configuratie:")
            filter_config = wijzigingen.get('filter_configuratie', {})
            print(f"   Verwijder termen: {filter_config.get('verwijder_termen', [])}")
            print(f"   Duplicaat strategie: {filter_config.get('duplicaat_strategie', 'laatste')}")
            print(f"   Verwijder sectie tekst: {filter_config.get('verwijder_sectie_tekst', True)}")
        else:
            print("❌ Geen wijzigingen data gevonden!")

    except Exception as e:
        print(f"❌ Fout bij verwerking: {e}")
        import traceback
        traceback.print_exc()

def test_configuratie_functionaliteit():
    """Test de configuratie functionaliteit"""

    print("\n🧪 Test: Configuratie functionaliteit")
    print("=" * 60)

    try:
        # Test huidige configuratie
        config = get_filter_config()
        print(f"✅ Huidige configuratie opgehaald")
        print(f"   Verwijder termen: {config.verwijder_termen}")
        print(f"   Duplicaat strategie: {config.duplicaat_strategie}")
        print(f"   Verwijder sectie tekst: {config.verwijder_sectie_tekst}")

        # Test configuratie update
        print("\n🔧 Test configuratie update...")
        update_filter_config(
            verwijder_termen=['samenvattend', 'theoretische onderbouwing', 'test term'],
            duplicaat_strategie='eerste',
            verwijder_sectie_tekst=True
        )

        updated_config = get_filter_config()
        print(f"✅ Configuratie geüpdatet")
        print(f"   Nieuwe verwijder termen: {updated_config.verwijder_termen}")
        print(f"   Nieuwe duplicaat strategie: {updated_config.duplicaat_strategie}")

    except Exception as e:
        print(f"❌ Fout bij configuratie test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_wijzigingen_functionaliteit()
    test_configuratie_functionaliteit()
