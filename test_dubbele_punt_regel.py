#!/usr/bin/env python3
"""
Test de nieuwe dubbele punt regel functionaliteit
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.api.portfolio_processor import (
    verwerk_portfolio_tekst, 
    get_filter_config, 
    set_filter_config,
    FilterConfig,
    TextReplacementRule,
    pas_tekst_vervangingen_toe,
    DEFAULT_FILTER_CONFIG
)

def test_dubbele_punt_regel():
    """Test de nieuwe dubbele punt regel functionaliteit"""
    
    print("🧪 Test: Dubbele Punt Regel Functionaliteit")
    print("=" * 60)
    
    # Test tekst met verschillende patronen
    test_tekst = """3.1 Competentie analyse

Dit is een voorbeeld van portfolio tekst.

Eigen regie en vertegenwoordiging bij verstandelijke beperking

Hier komt meer tekst na de titel.

Theoretische onderbouwing 1: Dit moet worden aangepast

3.2 Volgende sectie

Communicatie en samenwerking in multidisciplinaire teams

Nog meer tekst hier.

Samenvattend kan gesteld worden dat dit een test is.
"""

    print("📝 Test tekst:")
    print("   - 'Eigen regie en vertegenwoordiging bij verstandelijke beperking' (moet dubbele punt krijgen)")
    print("   - 'Communicatie en samenwerking in multidisciplinaire teams' (moet dubbele punt krijgen)")
    print("   - 'Theoretische onderbouwing 1:' (moet worden aangepast naar 'Theoretische onderbouwing:')")
    print()
    
    # Gebruik de default configuratie (die nu beide regels bevat)
    set_filter_config(DEFAULT_FILTER_CONFIG)
    
    print("🔧 Configuratie:")
    config = get_filter_config()
    print(f"   Aantal tekstvervanging regels: {len(config.tekst_vervangingen)}")
    for i, regel in enumerate(config.tekst_vervangingen, 1):
        print(f"   {i}. '{regel.zoek_patroon}' → '{regel.vervang_met}' (regex: {regel.is_regex})")
    print()
    
    # Test alleen de tekstvervanging functie
    print("🔍 Test tekstvervanging functie:")
    aangepaste_tekst, vervangingen = pas_tekst_vervangingen_toe(test_tekst)
    
    print(f"   Aantal vervangingen: {len(vervangingen)}")
    for i, vervanging in enumerate(vervangingen, 1):
        print(f"   {i}. {vervanging}")
    print()
    
    # Toon een deel van de aangepaste tekst
    print("📄 Aangepaste tekst (eerste 800 karakters):")
    print(aangepaste_tekst[:800])
    print("..." if len(aangepaste_tekst) > 800 else "")
    print()
    
    # Test volledige verwerking
    print("📝 Test volledige portfolio verwerking:")
    try:
        resultaat = verwerk_portfolio_tekst(test_tekst)
        
        print("✅ Verwerking succesvol!")
        print(f"📊 Aantal secties: {resultaat['total_sections']}")
        print()
        
        # Toon wijzigingen
        wijzigingen = resultaat.get('wijzigingen', {})
        
        print("🔄 Toegepaste tekstvervanging:")
        tekst_vervangingen = wijzigingen.get('toegepaste_tekst_vervangingen', [])
        if tekst_vervangingen:
            for i, vervanging in enumerate(tekst_vervangingen, 1):
                print(f"   {i}. {vervanging}")
        else:
            print("   Geen vervangingen toegepast")
        print()
        
        # Toon statistieken
        stats = wijzigingen.get('statistieken', {})
        print("📊 Statistieken:")
        print(f"   Toegepaste vervangingen: {stats.get('aantal_toegepaste_vervangingen', 0)}")
        print(f"   Verwijderde samenvattingen: {stats.get('aantal_verwijderde_samenvattend', 0)}")
        print(f"   Gedetecteerde secties: {stats.get('totaal_gedetecteerde_secties', 0)}")
        
    except Exception as e:
        print(f"❌ Fout bij verwerking: {str(e)}")
        import traceback
        traceback.print_exc()

def test_specifieke_gevallen():
    """Test specifieke gevallen voor de dubbele punt regel"""
    
    print("\n🧪 Test: Specifieke Gevallen voor Dubbele Punt Regel")
    print("=" * 60)
    
    test_gevallen = [
        "Eigen regie en vertegenwoordiging bij verstandelijke beperking",  # Moet dubbele punt krijgen
        "Communicatie en samenwerking",  # Moet dubbele punt krijgen
        "Theoretische onderbouwing 1:",  # Heeft al dubbele punt, moet niet aangepast
        "3.1 Sectie titel",  # Begint met nummer, moet niet aangepast
        "Dit is een zin.",  # Eindigt met punt, moet niet aangepast
        "HOOFDSTUK 1",  # Alleen hoofdletters, moet niet aangepast
        "a",  # Te kort, moet niet aangepast
        "Kwaliteit van zorg en ondersteuning"  # Moet dubbele punt krijgen
    ]
    
    # Gebruik alleen de dubbele punt regel
    dubbele_punt_regel = TextReplacementRule(
        zoek_patroon="^([A-Z][^:]*[a-z])$",
        vervang_met="\\1:",
        is_regex=True,
        prefix="",
        postfix=""
    )
    
    config = FilterConfig(
        verwijder_termen=[],
        tekst_vervangingen=[dubbele_punt_regel]
    )
    
    set_filter_config(config)
    
    print("Test gevallen:")
    for i, geval in enumerate(test_gevallen, 1):
        aangepaste_tekst, vervangingen = pas_tekst_vervangingen_toe(geval)
        status = "✅ AANGEPAST" if vervangingen else "⏸️  ONGEWIJZIGD"
        print(f"   {i}. '{geval}' → '{aangepaste_tekst}' {status}")

if __name__ == "__main__":
    test_dubbele_punt_regel()
    test_specifieke_gevallen()
    print("\n✨ Alle tests voltooid!")
