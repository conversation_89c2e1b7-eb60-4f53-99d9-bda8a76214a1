#!/usr/bin/env python3
"""
Test de nieuwe tekstvervanging functionaliteit via de API
"""

import requests
import json

def test_nieuwe_api_functionaliteit():
    """Test de nieuwe tekstvervanging via API"""
    
    base_url = "http://localhost:8800"
    
    print("🧪 Test: Nieuwe Tekstvervanging API Functionaliteit")
    print("=" * 60)
    
    # Test tekst met verschillende patronen
    test_tekst = """3.1 Competentie analyse

Dit is een voorbeeld van portfolio tekst.

Theoretische onderbouwing 1: Dit moet worden aangepast naar "Theoretische onderbouwing:"

Hier komt meer tekst.

Theoretische onderbouwing 2: Ook dit moet worden aangepast

3.2 Volgende sectie

Theoretische onderbouwing 3: En dit ook

Samenvattend kan gesteld worden dat dit een test is.
"""

    # 1. Test huidige configuratie ophalen
    print("📥 1. Huidige configuratie ophalen...")
    try:
        response = requests.get(f"{base_url}/config")
        if response.status_code == 200:
            config_data = response.json()
            print("✅ Configuratie opgehaald:")
            print(f"   Verwijder termen: {config_data['config']['verwijder_termen']}")
            print(f"   Tekstvervanging regels: {len(config_data['config']['tekst_vervangingen'])}")
            for i, regel in enumerate(config_data['config']['tekst_vervangingen'], 1):
                print(f"   {i}. '{regel['zoek_patroon']}' → '{regel['vervang_met']}' (regex: {regel['is_regex']})")
        else:
            print(f"❌ Fout bij ophalen configuratie: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Fout bij ophalen configuratie: {e}")
        return
    
    print()
    
    # 2. Test portfolio verwerking met nieuwe functionaliteit
    print("📝 2. Portfolio verwerking testen...")
    try:
        response = requests.post(f"{base_url}/process", 
                               json={"tekst": test_tekst},
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                data = result['data']
                wijzigingen = data.get('wijzigingen', {})
                
                print("✅ Verwerking succesvol!")
                print(f"📊 Aantal secties: {data['total_sections']}")
                print()
                
                # Toon toegepaste vervangingen
                vervangingen = wijzigingen.get('toegepaste_tekst_vervangingen', [])
                print("🔧 Toegepaste tekstvervanging:")
                if vervangingen:
                    for i, vervanging in enumerate(vervangingen, 1):
                        print(f"   {i}. {vervanging}")
                else:
                    print("   Geen vervangingen toegepast")
                print()
                
                # Toon statistieken
                stats = wijzigingen.get('statistieken', {})
                print("📊 Statistieken:")
                print(f"   Toegepaste vervangingen: {stats.get('aantal_toegepaste_vervangingen', 0)}")
                print(f"   Verwijderde samenvattingen: {stats.get('aantal_verwijderde_samenvattend', 0)}")
                print(f"   Gedetecteerde secties: {stats.get('totaal_gedetecteerde_secties', 0)}")
                print()
                
                # Toon configuratie die gebruikt werd
                filter_config = wijzigingen.get('filter_configuratie', {})
                tekst_vervangingen = filter_config.get('tekst_vervangingen', [])
                print("⚙️ Gebruikte configuratie:")
                print(f"   Aantal tekstvervanging regels: {len(tekst_vervangingen)}")
                for i, regel in enumerate(tekst_vervangingen, 1):
                    print(f"   {i}. '{regel['zoek_patroon']}' → '{regel['vervang_met']}' (regex: {regel['is_regex']})")
                
            else:
                print(f"❌ Verwerking mislukt: {result.get('error', 'Onbekende fout')}")
        else:
            print(f"❌ HTTP fout: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Fout bij verwerking: {e}")
    
    print()
    
    # 3. Test configuratie updaten met nieuwe regel
    print("🔧 3. Configuratie updaten met nieuwe regel...")
    try:
        nieuwe_config = {
            "verwijder_termen": ["samenvattend"],
            "prefix": "",
            "postfix": "",
            "duplicaat_strategie": "laatste",
            "verwijder_sectie_tekst": True,
            "tekst_vervangingen": [
                {
                    "zoek_patroon": "Theoretische onderbouwing \\d+:",
                    "vervang_met": "Theoretische onderbouwing:",
                    "is_regex": True,
                    "prefix": "",
                    "postfix": ""
                },
                {
                    "zoek_patroon": "Conclusie:",
                    "vervang_met": "Eindconclusie",
                    "is_regex": False,
                    "prefix": ">>> ",
                    "postfix": " <<<"
                }
            ]
        }
        
        response = requests.post(f"{base_url}/config",
                               json=nieuwe_config,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print("✅ Configuratie geüpdatet!")
                config = result['config']
                print(f"   Aantal tekstvervanging regels: {len(config['tekst_vervangingen'])}")
                for i, regel in enumerate(config['tekst_vervangingen'], 1):
                    print(f"   {i}. '{regel['zoek_patroon']}' → '{regel['vervang_met']}' (regex: {regel['is_regex']})")
            else:
                print(f"❌ Update mislukt: {result.get('error', 'Onbekende fout')}")
        else:
            print(f"❌ HTTP fout: {response.status_code}")
    except Exception as e:
        print(f"❌ Fout bij updaten configuratie: {e}")
    
    print()
    
    # 4. Test met nieuwe configuratie
    print("📝 4. Test met nieuwe configuratie...")
    test_tekst_2 = test_tekst + "\n\nConclusie: Dit is de eindconclusie van het portfolio."
    
    try:
        response = requests.post(f"{base_url}/process", 
                               json={"tekst": test_tekst_2},
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                wijzigingen = result['data'].get('wijzigingen', {})
                vervangingen = wijzigingen.get('toegepaste_tekst_vervangingen', [])
                
                print("✅ Verwerking met nieuwe configuratie succesvol!")
                print("🔧 Toegepaste vervangingen:")
                for i, vervanging in enumerate(vervangingen, 1):
                    print(f"   {i}. {vervanging}")
            else:
                print(f"❌ Verwerking mislukt: {result.get('error', 'Onbekende fout')}")
        else:
            print(f"❌ HTTP fout: {response.status_code}")
    except Exception as e:
        print(f"❌ Fout bij verwerking: {e}")

if __name__ == "__main__":
    test_nieuwe_api_functionaliteit()
    print("\n✨ API test voltooid!")
