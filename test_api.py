#!/usr/bin/env python3
"""
Test de API met de nieuwe wijzigingen functionaliteit
"""

import requests
import json

def test_api():
    url = "http://localhost:8800/process"

    test_data = {
        "tekst": """3.1 Dit is tekst die verwijderd moet worden

Dit is een voorbeeld van portfolio tekst die geanalyseerd wordt op competenties.

Samenvattend kan gesteld worden dat dit een test is.

Theoretische onderbouwing 1: Dit moet ook verwijderd worden

3.2 Ook deze tekst moet weg

Hier komt de tekst voor de volgende competentie.

Dit is een voorbeeld van portfolio tekst die geanalyseerd wordt op competenties.

Samenvattend is dit een duplicaat test."""
    }

    print("🧪 Testing API met wijzigingen functionaliteit...")
    print("=" * 50)

    try:
        response = requests.post(url, json=test_data)

        if response.status_code == 200:
            result = response.json()

            if result.get('success'):
                data = result['data']
                print("✅ API test succesvol!")
                print(f"📊 Aantal secties: {data['total_sections']}")

                if 'wijzigingen' in data:
                    wijzigingen = data['wijzigingen']
                    stats = wijzigingen['statistieken']

                    print("\n🔍 Wijzigingen overzicht:")
                    print(f"   📝 Gedetecteerde secties: {stats['totaal_gedetecteerde_secties']}")
                    print(f"   🗑️ Verwijderde samenvattingen: {stats['aantal_verwijderde_samenvattend']}")
                    print(f"   🔄 Verwijderde duplicaten: {stats['aantal_verwijderde_duplicaten']}")

                    print("\n📋 Verwijderde samenvattende paragrafen:")
                    for i, paragraaf in enumerate(wijzigingen['verwijderde_samenvattend_paragrafen'], 1):
                        print(f"   {i}. {paragraaf}")

                    print("\n🏗️ Sectie structuur:")
                    for sectie in wijzigingen['sectie_structuur']:
                        print(f"   {sectie['nummer']}: {sectie['paragraaf_count']} paragrafen")

                    print("\n✅ Wijzigingen functionaliteit werkt perfect!")
                else:
                    print("❌ Geen wijzigingen data gevonden!")
            else:
                print(f"❌ API fout: {result.get('error')}")
        else:
            print(f"❌ HTTP fout: {response.status_code}")
            print(response.text)

    except Exception as e:
        print(f"❌ Fout bij API test: {e}")

def test_config_api():
    """Test de configuratie API endpoints"""

    print("\n🧪 Testing Configuratie API...")
    print("=" * 50)

    try:
        # Test configuratie ophalen
        response = requests.get("http://localhost:8800/config")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Configuratie ophalen succesvol!")
                config = result['config']
                print(f"   Verwijder termen: {config['verwijder_termen']}")
                print(f"   Duplicaat strategie: {config['duplicaat_strategie']}")
                print(f"   Verwijder sectie tekst: {config['verwijder_sectie_tekst']}")
            else:
                print(f"❌ Configuratie ophalen fout: {result.get('error')}")
        else:
            print(f"❌ HTTP fout bij configuratie ophalen: {response.status_code}")

        # Test configuratie updaten
        print("\n🔧 Test configuratie update...")
        new_config = {
            "verwijder_termen": ["samenvattend", "theoretische onderbouwing", "test term"],
            "prefix": "",
            "postfix": ":",
            "duplicaat_strategie": "eerste",
            "verwijder_sectie_tekst": True
        }

        response = requests.post("http://localhost:8800/config", json=new_config)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Configuratie update succesvol!")
                print(f"   Nieuwe configuratie: {result['config']}")
            else:
                print(f"❌ Configuratie update fout: {result.get('error')}")
        else:
            print(f"❌ HTTP fout bij configuratie update: {response.status_code}")

        # Test configuratie reset
        print("\n🔄 Test configuratie reset...")
        response = requests.post("http://localhost:8800/config/reset")
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Configuratie reset succesvol!")
                print(f"   Reset configuratie: {result['config']}")
            else:
                print(f"❌ Configuratie reset fout: {result.get('error')}")
        else:
            print(f"❌ HTTP fout bij configuratie reset: {response.status_code}")

    except Exception as e:
        print(f"❌ Fout bij configuratie API test: {e}")

if __name__ == "__main__":
    test_api()
    test_config_api()
