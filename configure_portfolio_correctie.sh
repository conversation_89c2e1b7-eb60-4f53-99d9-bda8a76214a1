#!/bin/bash

# Portfolio Correctie Systeem - Configuratiescript voor Ubuntu 22.04 (Deel 2)
# --------------------------------------------------------------------------
# Dit script moet op de server worden uitgevoerd na het kopiëren van de bestanden!

echo "🚀 Portfolio Correctie Systeem - Server Configuratie (Deel 2)"
echo "=========================================================="

# Activeer virtual environment
cd /opt/portfolio_correctie
source venv/bin/activate

# Installeer Python dependencies
echo "📦 Python dependencies installeren..."
cd backend
pip install -r requirements.txt
cd ..

# Configureer Nginx
echo "🌐 Nginx configureren..."
cat > /tmp/portfolio_correctie_nginx << 'EOL'
server {
    listen 80;
    server_name jouw_domein_of_ip;

    location / {
        root /opt/portfolio_correctie/frontend;
        index index.html;
        try_files $uri $uri/ =404;
    }

    location /api/ {
        proxy_pass http://localhost:8800/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOL

sudo mv /tmp/portfolio_correctie_nginx /etc/nginx/sites-available/portfolio_correctie
sudo ln -sf /etc/nginx/sites-available/portfolio_correctie /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl restart nginx

# Configureer systemd service
echo "🔒 Systemd service configureren..."
cat > /tmp/portfolio-correctie.service << EOL
[Unit]
Description=Portfolio Correctie Backend
After=network.target

[Service]
User=$USER
WorkingDirectory=/opt/portfolio_correctie/backend
ExecStart=/opt/portfolio_correctie/venv/bin/python start_server.py
Restart=always

[Install]
WantedBy=multi-user.target
EOL

sudo mv /tmp/portfolio-correctie.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable portfolio-correctie.service
sudo systemctl start portfolio-correctie.service

echo "✨ Installatie voltooid! Je applicatie draait nu op:"
echo "   Frontend: http://jouw_domein_of_ip/"
echo "   Backend API: http://jouw_domein_of_ip/api/"
echo "   API Docs: http://jouw_domein_of_ip/api/docs"
echo ""
echo "💡 Tip: Vervang 'jouw_domein_of_ip' in de Nginx configuratie met je echte domein of IP-adres:"
echo "   sudo nano /etc/nginx/sites-available/portfolio_correctie"
echo "   sudo systemctl restart nginx"